Stack trace:
Frame         Function      Args
0007FFFF9850  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF9850, 0007FFFF8750) msys-2.0.dll+0x2118E
0007FFFF9850  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9850  0002100469F2 (00021028DF99, 0007FFFF9708, 0007FFFF9850, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9850  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9850  00021006A545 (0007FFFF9860, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9860, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF839970000 ntdll.dll
7FF838C60000 KERNEL32.DLL
7FF837490000 KERNELBASE.dll
7FF837E20000 USER32.dll
7FF837150000 win32u.dll
7FF8388E0000 GDI32.dll
7FF837180000 gdi32full.dll
7FF837000000 msvcp_win.dll
7FF837790000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF837990000 advapi32.dll
7FF8397B0000 msvcrt.dll
7FF839660000 sechost.dll
7FF837CF0000 RPCRT4.dll
7FF837890000 bcrypt.dll
7FF8367F0000 CRYPTBASE.DLL
7FF8372A0000 bcryptPrimitives.dll
7FF839090000 IMM32.DLL
