import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

// Async thunks for API calls
export const fetchEducatorFeedbacks = createAsyncThunk(
  "educatorFeedback/fetchFeedbacks",
  async (params, { rejectWithValue }) => {
    try {
      // TODO: Implement actual API call
      // const response = await api.get("/api/educator/feedbacks", { params });
      // return response.data;
      
      // Mock data for now
      return {
        feedbacks: [
          {
            id: 1,
            student_id: 1,
            student_name: "<PERSON>",
            rating: 4,
            categories: ["academic", "behavior"],
            comment: "Excellent performance in mathematics.",
            status: "approved",
            created_at: "2025-01-15T10:30:00Z",
          },
        ],
        total: 1,
      };
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const submitEducatorFeedback = createAsyncThunk(
  "educatorFeedback/submitFeedback",
  async (feedbackData, { rejectWithValue }) => {
    try {
      // TODO: Implement actual API call
      // const response = await api.post("/api/educator/feedbacks", feedbackData);
      // return response.data;
      
      // Mock response
      return {
        id: Date.now(),
        ...feedbackData,
        status: "pending",
        created_at: new Date().toISOString(),
      };
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateEducatorFeedback = createAsyncThunk(
  "educatorFeedback/updateFeedback",
  async ({ id, updates }, { rejectWithValue }) => {
    try {
      // TODO: Implement actual API call
      // const response = await api.put(`/api/educator/feedbacks/${id}`, updates);
      // return response.data;
      
      // Mock response
      return { id, ...updates };
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteEducatorFeedback = createAsyncThunk(
  "educatorFeedback/deleteFeedback",
  async (feedbackId, { rejectWithValue }) => {
    try {
      // TODO: Implement actual API call
      // await api.delete(`/api/educator/feedbacks/${feedbackId}`);
      return feedbackId;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  feedbacks: [],
  loading: false,
  error: null,
  submitting: false,
  submitError: null,
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
  },
  filters: {
    student_id: null,
    status: "all", // all, pending, approved, rejected
    category: "all",
    date_range: null,
  },
};

const educatorFeedbackSlice = createSlice({
  name: "educatorFeedback",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
      state.pagination.page = 1; // Reset to first page when filters change
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
      state.pagination.page = 1;
    },
    setPage: (state, action) => {
      state.pagination.page = action.payload;
    },
    clearError: (state) => {
      state.error = null;
      state.submitError = null;
    },
    clearFeedbacks: (state) => {
      state.feedbacks = [];
      state.pagination = initialState.pagination;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch feedbacks
      .addCase(fetchEducatorFeedbacks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEducatorFeedbacks.fulfilled, (state, action) => {
        state.loading = false;
        const { feedbacks, total } = action.payload;
        
        if (state.pagination.page === 1) {
          state.feedbacks = feedbacks;
        } else {
          state.feedbacks = [...state.feedbacks, ...feedbacks];
        }
        
        state.pagination.total = total;
        state.pagination.hasMore = feedbacks.length === state.pagination.pageSize;
      })
      .addCase(fetchEducatorFeedbacks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Submit feedback
      .addCase(submitEducatorFeedback.pending, (state) => {
        state.submitting = true;
        state.submitError = null;
      })
      .addCase(submitEducatorFeedback.fulfilled, (state, action) => {
        state.submitting = false;
        state.feedbacks.unshift(action.payload); // Add to beginning of list
        state.pagination.total += 1;
      })
      .addCase(submitEducatorFeedback.rejected, (state, action) => {
        state.submitting = false;
        state.submitError = action.payload;
      })
      
      // Update feedback
      .addCase(updateEducatorFeedback.fulfilled, (state, action) => {
        const index = state.feedbacks.findIndex(f => f.id === action.payload.id);
        if (index !== -1) {
          state.feedbacks[index] = { ...state.feedbacks[index], ...action.payload };
        }
      })
      
      // Delete feedback
      .addCase(deleteEducatorFeedback.fulfilled, (state, action) => {
        state.feedbacks = state.feedbacks.filter(f => f.id !== action.payload);
        state.pagination.total -= 1;
      });
  },
});

export const {
  setFilters,
  clearFilters,
  setPage,
  clearError,
  clearFeedbacks,
} = educatorFeedbackSlice.actions;

export default educatorFeedbackSlice.reducer;

// Selectors
export const selectEducatorFeedbacks = (state) => state.educatorFeedback.feedbacks;
export const selectEducatorFeedbackLoading = (state) => state.educatorFeedback.loading;
export const selectEducatorFeedbackError = (state) => state.educatorFeedback.error;
export const selectEducatorFeedbackSubmitting = (state) => state.educatorFeedback.submitting;
export const selectEducatorFeedbackFilters = (state) => state.educatorFeedback.filters;
export const selectEducatorFeedbackPagination = (state) => state.educatorFeedback.pagination;
