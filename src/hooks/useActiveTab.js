import { usePathname } from "expo-router";

export const useActiveTab = () => {
  const pathname = usePathname();

  // Map routes to tab IDs for new navigation structure
  const routeToTab = {
    "/authenticated/parent": "activityFeed", // Default route shows Activity Feed
    "/authenticated/parent/school-calendar": "schoolCalendar",
    "/authenticated/parent/student-growth": "studentGrowth",
    "/authenticated/parent/student-profile": "studentProfile",
    "/authenticated/parent/notifications-messages": "notifications",

    // Legacy routes for backward compatibility
    "/authenticated/parent/school-life": "activityFeed",
    "/authenticated/parent/educator-feedback": "notifications",
    "/authenticated/parent/calendar": "schoolCalendar",
    "/authenticated/parent/academic": "studentGrowth",
    "/authenticated/parent/performance": "studentGrowth",
  };

  return routeToTab[pathname] || "activityFeed";
};

export default useActiveTab;
