import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
} from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import StarRating from "react-native-star-rating-widget";
import { theme } from "../../../../../styles/theme";
import { USER_CATEGORIES } from "../../../../../constants/userCategories";

const EducatorFeedbackDrawer = () => {
  const dispatch = useDispatch();
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [rating, setRating] = useState(0);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [comment, setComment] = useState("");
  const [existingFeedbacks, setExistingFeedbacks] = useState([]);

  // Get global state
  const { sessionData } = useSelector((state) => state.app);

  // Mock student data for educator's class
  const classStudents = [
    {
      id: 1,
      student_calling_name: "John Doe",
      admission_number: "2024001",
      grade: "Grade 5",
      profile_picture: null,
    },
    {
      id: 2,
      student_calling_name: "Jane Smith",
      admission_number: "2024002",
      grade: "Grade 5",
      profile_picture: null,
    },
    {
      id: 3,
      student_calling_name: "Mike Johnson",
      admission_number: "2024003",
      grade: "Grade 5",
      profile_picture: null,
    },
  ];

  // Feedback categories
  const feedbackCategories = [
    { id: "academic", label: "Academic", color: "#4CAF50" },
    { id: "behavior", label: "Behavior", color: "#2196F3" },
    { id: "social", label: "Social", color: "#FF9800" },
    { id: "creative", label: "Creative", color: "#9C27B0" },
    { id: "sports", label: "Sports", color: "#F44336" },
  ];

  // Mock existing feedbacks
  useEffect(() => {
    setExistingFeedbacks([
      {
        id: 1,
        student: classStudents[0],
        rating: 4,
        categories: ["academic", "behavior"],
        comment: "Excellent performance in mathematics. Shows great improvement in problem-solving skills.",
        status: "Approved",
        created_by: "Ms. Sarah Johnson",
        approved_by: "Principal Mr. Asanka",
        created_at: "2025-01-15T10:30:00Z",
        revision_instructions: null,
      },
      {
        id: 2,
        student: classStudents[1],
        rating: 3,
        categories: ["social", "creative"],
        comment: "Good participation in group activities. Creative thinking in art projects.",
        status: "Revision Pending",
        created_by: "Ms. Sarah Johnson",
        approved_by: null,
        created_at: "2025-01-14T14:20:00Z",
        revision_instructions: "Please provide more specific examples of creative work.",
      },
    ]);
  }, []);

  const toggleCategory = (categoryId) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleSubmitFeedback = () => {
    if (!selectedStudent || rating === 0 || selectedCategories.length === 0 || !comment.trim()) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    // TODO: Implement API call to submit feedback
    Alert.alert("Success", "Feedback submitted successfully");
    
    // Reset form
    setSelectedStudent(null);
    setRating(0);
    setSelectedCategories([]);
    setComment("");
    setShowAddForm(false);
  };

  const renderStudentSelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Select Student</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.studentScroll}>
        {classStudents.map((student) => (
          <TouchableOpacity
            key={student.id}
            style={[
              styles.studentCard,
              selectedStudent?.id === student.id && styles.selectedStudentCard,
            ]}
            onPress={() => setSelectedStudent(student)}
          >
            <View style={styles.studentAvatar}>
              {student.profile_picture ? (
                <Image source={{ uri: student.profile_picture }} style={styles.avatarImage} />
              ) : (
                <MaterialIcons name="person" size={24} color={theme.colors.textSecondary} />
              )}
            </View>
            <Text style={styles.studentName}>{student.student_calling_name}</Text>
            <Text style={styles.studentInfo}>{student.admission_number}</Text>
            <Text style={styles.studentInfo}>{student.grade}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderRatingSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Rating</Text>
      <View style={styles.ratingContainer}>
        <StarRating
          rating={rating}
          onChange={setRating}
          starSize={32}
          color={theme.colors.primary}
          emptyColor={theme.colors.border}
        />
        <Text style={styles.ratingText}>{rating}.0</Text>
      </View>
    </View>
  );

  const renderCategoriesSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Categories</Text>
      <View style={styles.categoriesContainer}>
        {feedbackCategories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              selectedCategories.includes(category.id) && {
                backgroundColor: category.color,
              },
            ]}
            onPress={() => toggleCategory(category.id)}
          >
            <Text
              style={[
                styles.categoryText,
                selectedCategories.includes(category.id) && styles.selectedCategoryText,
              ]}
            >
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderCommentSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Comment</Text>
      <TextInput
        style={styles.commentInput}
        multiline
        numberOfLines={4}
        placeholder="Enter your feedback comment..."
        value={comment}
        onChangeText={setComment}
        textAlignVertical="top"
      />
    </View>
  );

  const renderAddForm = () => (
    <View style={styles.addFormContainer}>
      <View style={styles.formHeader}>
        <Text style={styles.formTitle}>Add New Feedback</Text>
        <TouchableOpacity onPress={() => setShowAddForm(false)}>
          <MaterialIcons name="close" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {renderStudentSelector()}
        {renderRatingSection()}
        {renderCategoriesSection()}
        {renderCommentSection()}

        <View style={styles.formActions}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => setShowAddForm(false)}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.saveButton} onPress={handleSubmitFeedback}>
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Educator Feedbacks</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddForm(true)}
        >
          <MaterialIcons name="add" size={20} color="white" />
          <Text style={styles.addButtonText}>Add Feedback</Text>
        </TouchableOpacity>
      </View>

      {showAddForm ? renderAddForm() : (
        <ScrollView style={styles.feedbacksList} showsVerticalScrollIndicator={false}>
          {existingFeedbacks.map((feedback) => (
            <View key={feedback.id} style={styles.feedbackCard}>
              {/* Feedback content will be implemented in the next part */}
              <Text>Feedback for {feedback.student.student_calling_name}</Text>
            </View>
          ))}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  addButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: theme.spacing.xs,
  },
  addFormContainer: {
    flex: 1,
    padding: theme.spacing.md,
  },
  formHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.lg,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
  },
  section: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  studentScroll: {
    flexDirection: "row",
  },
  studentCard: {
    backgroundColor: theme.colors.card,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.sm,
    alignItems: "center",
    minWidth: 100,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedStudentCard: {
    borderColor: theme.colors.primary,
  },
  studentAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.background,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: theme.spacing.xs,
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  studentName: {
    fontSize: 12,
    fontWeight: "600",
    color: theme.colors.text,
    textAlign: "center",
  },
  studentInfo: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    textAlign: "center",
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  ratingText: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
    marginLeft: theme.spacing.md,
  },
  categoriesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  categoryChip: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.background,
    marginRight: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  categoryText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  selectedCategoryText: {
    color: "white",
    fontWeight: "600",
  },
  commentInput: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    fontSize: 14,
    color: theme.colors.text,
    minHeight: 100,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  formActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: theme.spacing.lg,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  cancelButtonText: {
    textAlign: "center",
    fontSize: 16,
    fontWeight: "600",
    color: theme.colors.text,
  },
  saveButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginLeft: theme.spacing.sm,
  },
  saveButtonText: {
    textAlign: "center",
    fontSize: 16,
    fontWeight: "600",
    color: "white",
  },
  feedbacksList: {
    flex: 1,
    padding: theme.spacing.md,
  },
  feedbackCard: {
    backgroundColor: theme.colors.card,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});

export default EducatorFeedbackDrawer;
