// Navigation utility for Expo Router
import { router } from "expo-router";

export const handleNavigationPress = (tabId, currentScreen = "unknown") => {
  console.log(`🚀 NAVIGATING: ${currentScreen} -> ${tabId}`);

  try {
    switch (tabId) {
      case "activityFeed":
        console.log("✅ Navigate to Activity Feed");
        console.log("🔄 Calling router.push('/authenticated/parent/')");
        router.push("/authenticated/parent/");
        break;
      case "schoolCalendar":
        console.log("✅ Navigate to School Calendar");
        console.log(
          "🔄 Calling router.push('/authenticated/parent/school-calendar')"
        );
        router.push("/authenticated/parent/school-calendar");
        break;
      case "studentGrowth":
        console.log("✅ Navigate to Student Growth");
        console.log(
          "🔄 Calling router.push('/authenticated/parent/student-growth')"
        );
        router.push("/authenticated/parent/student-growth");
        break;
      case "studentProfile":
        console.log("✅ Navigate to Student Profile");
        console.log(
          "🔄 Calling router.push('/authenticated/parent/student-profile')"
        );
        router.push("/authenticated/parent/student-profile");
        break;
      case "notifications":
        console.log("✅ Navigate to Notifications & Messages");
        console.log(
          "🔄 Calling router.push('/authenticated/parent/notifications-messages')"
        );
        router.push("/authenticated/parent/notifications-messages");
        break;
      // Legacy routes for backward compatibility
      case "schoolLife":
        console.log("✅ Navigate to School Life (Legacy)");
        console.log(
          "🔄 Calling router.push('/authenticated/parent/school-life')"
        );
        router.push("/authenticated/parent/school-life");
        break;
      case "feedback":
        console.log("✅ Navigate to Educator Feedback (Legacy)");
        console.log(
          "🔄 Calling router.push('/authenticated/parent/educator-feedback')"
        );
        router.push("/authenticated/parent/educator-feedback");
        break;
      case "calendar":
        console.log("✅ Navigate to Calendar (Legacy)");
        console.log("🔄 Calling router.push('/authenticated/parent/calendar')");
        router.push("/authenticated/parent/calendar");
        break;
      case "academic":
        console.log("✅ Navigate to Academic Performance (Legacy)");
        console.log("🔄 Calling router.push('/authenticated/parent/academic')");
        router.push("/authenticated/parent/academic");
        break;
      case "performance":
        console.log("✅ Navigate to Student Performance (Legacy)");
        console.log(
          "🔄 Calling router.push('/authenticated/parent/performance')"
        );
        router.push("/authenticated/parent/performance");
        break;
      default:
        console.log(`❓ Unknown navigation target: ${tabId}`);
        break;
    }
  } catch (error) {
    console.error("❌ Navigation error:", error);
  }
};

// Navigation mapping for future implementation
export const navigationRoutes = {
  // New navigation structure
  activityFeed: "/authenticated/parent/",
  schoolCalendar: "/authenticated/parent/school-calendar",
  studentGrowth: "/authenticated/parent/student-growth",
  studentProfile: "/authenticated/parent/student-profile",
  notifications: "/authenticated/parent/notifications-messages",
  // Legacy routes for backward compatibility
  schoolLife: "/authenticated/parent/school-life",
  feedback: "/authenticated/parent/educator-feedback",
  calendar: "/authenticated/parent/calendar",
  academic: "/authenticated/parent/academic",
  performance: "/authenticated/parent/performance",
};

export default handleNavigationPress;
